from enum import Enum
from pathlib import Path

from pydantic import BaseModel


class APPENV(str, Enum):
    local = "local"  # for local debug
    dev = "dev"  # for dev backend/frontend integration
    staging = "staging"  # for QA
    prod = "prod"  # for production


class LoggerConfig(BaseModel):
    name: str
    level: str


class LoggingConfig(BaseModel):
    loggers: list[LoggerConfig]
    format: str


class IngestConfig(BaseModel):
    bf_bin_path: Path


class S3Config(BaseModel):
    key_id: str
    key_secret: str
    region: str
    endpoint: str


class MetaConfig(BaseModel):
    data_root_path: Path
    ingest: IngestConfig
    s3: S3Config


class AppConfig(BaseModel):
    app_env: APPENV
    logging: LoggingConfig
    meta: MetaConfig
