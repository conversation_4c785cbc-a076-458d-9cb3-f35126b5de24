import time
from collections.abc import Sequence
from pathlib import Path

import numpy as np
from pydantic import PositiveInt

from minions.domain.analysis.model import MergeChannelsReq
from minions.domain.ingest.svc import IngestSvc
from minions.domain.query.img.svc import ImgSvc
from minions.domain.task.model import TaskDefaultLogFileName
from minions.infra.logging.loguru import FLogger
from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import AxesData
from minions.infra.tiledb.readers.merge.channel.base import (
    ChannelInfo,
    ChannelMergeSource,
    DefaultChannelMergeSource,
    OutputInfo,
)
from minions.infra.tiledb.readers.merge.channel.reader import (
    ChannelMergeReader,
    MergeInfo,
)
from minions.infra.tiledb.readers.tdb import TdbReader


class ChannelMergeSvc:
    def __init__(self, img_svc: ImgSvc, ingest_svc: IngestSvc) -> None:
        self.img_svc = img_svc
        self.ingest_svc = ingest_svc

    def merge_channels(self, req: MergeChannelsReq):
        if not isinstance(req, MergeChannelsReq):
            req = MergeChannelsReq.model_validate(req)

        total_start = time.perf_counter()
        f_logger = self.setup_logger_for_file(req.output_folder_id)
        f_logger.info("request => {}", req)

        try:
            f_logger.info("===== Merge Images =====")
            start = time.perf_counter()

            self._do_merge(req)

            end = time.perf_counter()
            f_logger.info("Time spent {} secs", end - start)
            f_logger.info("===== Total =====\nTotal time spent {} secs", end - total_start)
        except Exception as e:
            f_logger.info("process failed: {}", e)
            raise

    def _do_merge(self, req: MergeChannelsReq):
        merge_sources = []
        fst_merge_img = next(iter(req.merge_imgs), None)
        if fst_merge_img is None:
            return

        for merge_img in req.merge_imgs:
            tdb_group_uri = self.img_svc.get_tdb_group_uri(merge_img.id)
            channel_infos = [ChannelInfo(id=channel_id) for channel_id in merge_img.channels]

            merge_source = DefaultChannelMergeSource(
                tdb_group_uri=tdb_group_uri,
                channel_infos=channel_infos,
            )
            merge_sources.append(merge_source)

        tmp_logs_dir = self.img_svc.get_item_store_path(req.output_folder_id)
        output_info = self.generate_output_info(fst_merge_img.id, tmp_logs_dir)
        self.merge(merge_sources, output_info, req.output_img_id)

    def merge(
        self,
        merge_sources: Sequence[ChannelMergeSource],
        output_info: OutputInfo,
        output_img_id: str,
    ):
        merge_info = MergeInfo(inputs=merge_sources, output=output_info)
        with ChannelMergeReader(merge_info) as channel_merge_reader:
            self.ingest_svc.to_tdb(channel_merge_reader, output_img_id)

    def setup_logger_for_file(self, output_folder_id: str):
        log_file_path = self.img_svc.get_item_store_path(output_folder_id).joinpath(TaskDefaultLogFileName)
        return FLogger(log_file_path)

    def generate_output_info(self, img_id: str, tmp_logs_dir: Path):
        with TdbReader(self.img_svc.get_tdb_group_uri(img_id)) as tdb:
            img_meta = tdb.image_metadata()
            img_axes_shape = tdb.level_shape()

            output_info = OutputInfo(
                origin=AxesData[float].from_dict({Axes.X: 0.0, Axes.Y: 0.0}),
                spacing=AxesData[float].from_dict(
                    {
                        Axes.X: img_meta.PhysicalSizeX or 1.0,
                        Axes.Y: img_meta.PhysicalSizeY or 1.0,
                    },
                ),
                size=AxesData[PositiveInt].from_dict(
                    {
                        Axes.X: img_axes_shape.raw[Axes.X],
                        Axes.Y: img_axes_shape.raw[Axes.Y],
                    },
                ),
                unit=AxesData[str].from_dict(
                    {
                        Axes.X: img_meta.PhysicalSizeXUnit or "",
                        Axes.Y: img_meta.PhysicalSizeYUnit or "",
                    },
                ),
                dtype=np.dtype(tdb.level_dtype()),
                tmp_logs_dir=tmp_logs_dir,
            )

            return output_info
