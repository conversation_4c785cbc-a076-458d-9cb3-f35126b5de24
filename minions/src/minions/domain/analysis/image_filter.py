from pathlib import Path

import psutil

from minions.domain.analysis.model import (
    SaveAsReq,
    SupportedSaveAsType,
)
from minions.domain.common.error import DomainError, DomainErrorCode
from minions.domain.disk.svc import DiskSvc
from minions.domain.ingest.svc import IngestSvc
from minions.domain.tdb.svc import TdbSvc
from minions.infra.tiledb.filters.common import Clip<PERSON>ilter, RescaleFilter
from minions.infra.tiledb.readers.bit_depth_convert import BitDepthConvertReader
from minions.infra.tiledb.readers.filter import FilterReader
from minions.infra.tiledb.readers.region import RegionReader
from minions.infra.tiledb.writers.colored_img import ColoredImgWriter
from minions.infra.tiledb.writers.ome_tiff import OMETiffWriter
from minions.infra.tiledb.writers.tdb import TdbWriter


class ImageFilterSvc:
    def __init__(self, ingest_svc: IngestSvc, disk_svc: DiskSvc, tdb_svc: TdbSvc):
        self.disk_svc = disk_svc
        self.tdb_svc = tdb_svc
        self.ingest_svc = ingest_svc

    def get_reader_from_save_req(self, req: SaveAsReq):
        src_tdb_uri = req.src_img_id
        src_tdb = self.tdb_svc.get_tdb(src_tdb_uri)
        for channel_ouput in req.channel_outputs:
            channel_ids = [f.idx for f in channel_ouput.channel_filters]
            region = req.get_reader_region(channel_ids)
            region_reader = RegionReader(src_tdb, region)
            final_reader = region_reader
            clip_arr = channel_ouput.get_clip_arr()

            if clip_arr:
                clip_filter = ClipFilter(range_a=clip_arr)
                final_reader = FilterReader(region_reader, clip_filter)

            rescale_arr = channel_ouput.get_rescale_arr()
            if rescale_arr:
                rescale_filter = RescaleFilter(range_a=rescale_arr, cores=psutil.cpu_count() or 1)
                final_reader = FilterReader(region_reader, rescale_filter)

            if req.tgt_bit_depth:
                final_reader = BitDepthConvertReader(region_reader, req.tgt_bit_depth)

            yield channel_ouput.sub_path, final_reader

    def save_as(self, req: SaveAsReq):
        if not isinstance(req, SaveAsReq):
            req = SaveAsReq.model_validate(req)

        for output_path, img_reader in self.get_reader_from_save_req(req):
            writer = None
            match req.tgt_type:
                case SupportedSaveAsType.tiff:
                    output_path_w = Path(output_path)
                    output_path_w = self.disk_svc.resolve_to_irs_disk_path(output_path_w, "w")
                    writer = OMETiffWriter(output_path_w)
                case SupportedSaveAsType.irs:
                    tgt_tdb_uri = output_path
                    writer = TdbWriter(self.tdb_svc, output_group_uri=tgt_tdb_uri)
                case SupportedSaveAsType.jpg:
                    output_path_w = Path(output_path)
                    output_path_w = self.disk_svc.resolve_to_irs_disk_path(output_path_w, "w")
                    writer = ColoredImgWriter(output_path_w)
                case _:
                    raise DomainError(
                        DomainErrorCode.export_not_support,
                        save_as_type=req.tgt_type,
                    )

            with img_reader:
                writer.write_from(img_reader)
