from __future__ import annotations

from enum import StrEnum

from pydantic import BaseModel

from minions.infra.tiledb.readers.region import Region, Segment


class ChannelFilter(BaseModel):
    idx: int
    rescale_range: tuple[float, float] | None
    clip_range: tuple[float, float] | None


class ChannelOutput(BaseModel):
    sub_path: str
    channel_filters: list[ChannelFilter]

    def get_clip_arr(self):
        clip_ranges = [
            f.clip_range for f in sorted(self.channel_filters, key=lambda x: x.idx) if f.clip_range is not None
        ]
        return clip_ranges

    def get_rescale_arr(self):
        rescale_ranges = [
            f.rescale_range for f in sorted(self.channel_filters, key=lambda x: x.idx) if f.rescale_range is not None
        ]
        return rescale_ranges


class XYRegion(BaseModel):
    x: int
    y: int
    width: int
    height: int

    def get_segments(self):
        x_segment = Segment(start=self.x, length=self.width)
        y_segment = Segment(start=self.y, length=self.height)
        return x_segment, y_segment


class SaveAsReq(BaseModel):
    src_img_id: str
    channel_outputs: list[ChannelOutput]
    xy_region: XYRegion | None = None
    tgt_type: SupportedSaveAsType
    tgt_bit_depth: str | None = None

    def get_reader_region(self, channel_ids: list[int]):
        if self.xy_region is None:
            return Region(channel_ids=channel_ids)
        else:
            x_seg, y_seg = self.xy_region.get_segments()
            return Region(x_segment=x_seg, y_segment=y_seg, channel_ids=channel_ids)


tiff_suffix = ".tiff"
irs_suffix = ".irs"
jpg_suffix = ".jpg"


class SupportedSaveAsType(StrEnum):
    tiff = "tiff"
    irs = "irs"
    jpg = "jpg"

    def to_suffix(self):
        suffix = ""
        match self:
            case SupportedSaveAsType.tiff:
                suffix = tiff_suffix
            case SupportedSaveAsType.irs:
                suffix = irs_suffix
            case SupportedSaveAsType.jpg:
                suffix = jpg_suffix
        return suffix


class MergeChannelsReq(BaseModel):
    output_folder_id: str
    output_img_id: str
    merge_imgs: list[MergeImg]

    class MergeImg(BaseModel):
        id: str
        channels: list[int]
