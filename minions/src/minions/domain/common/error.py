from enum import StrEnum, auto


class DomainErrorCode(StrEnum):
    """Do not change name once it has been created, unless use an explict value."""

    convert_to_ome_failed = auto()
    convert_to_tiledb_failed = auto()
    missing_level = auto()
    missing_level_in_cache = auto()
    missing_group = auto()
    get_omexml_failed = auto()
    axes_not_support = auto()

    # region merge
    merge_channel_names_not_unique = auto()
    merge_output_axes_inconsistent = auto()
    # endregion

    proj_not_found = auto()
    item_beyond_storage_limit = auto()
    item_not_found = auto()
    item_name_conflict = auto()
    item_type_flag_not_matched = auto()
    item_can_not_do_on_proj = auto()
    item_tgt_can_not_be_child_folder = auto()
    item_operation_not_support = auto()
    item_task_not_done = auto()
    task_not_found = auto()
    task_retry_not_support = auto()

    elastix_dtype_not_support = auto()
    elastix_spacing_unit_not_consistent = auto()

    export_not_support = auto()

    team_not_empty = auto()
    team_not_found = auto()

    user_name_conflict = auto()
    user_beyond_limit = auto()
    user_online_beyond_limit = auto()
    user_not_found = auto()
    user_password_not_matched = auto()

    license_invalid = auto()
    license_already_initialized = auto()

    tdb_cache_missed = auto()

    ray_api_failed = auto()
    ray_api_timeout = auto()


class DomainError(Exception):
    def __init__(self, error_code: DomainErrorCode, *args, **kwargs) -> None:
        if not isinstance(error_code, DomainErrorCode):
            msg = "error_code should be DomainErrorCode"
            raise TypeError(msg)
        if kwargs:
            super().__init__(error_code, *args, kwargs)
        else:
            super().__init__(error_code, *args)
        self.error_code = error_code

    def __str__(self) -> str:
        msg = [str(arg) for arg in self.args]
        return " ".join(msg)
