import time
import tracemalloc
from contextlib import contextmanager
from datetime import UTC, datetime, timedelta

from loguru import logger


def utc_now():
    return datetime.now(UTC)


def unixtime_now_in_ms():
    return to_unixtime_in_ms(utc_now())


def to_unixtime_in_ms(dt: datetime):
    return int(dt.timestamp() * 1e3)


@contextmanager
def timing(msg=None):
    logger.opt(depth=2).debug("start timing for {}", msg)
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    duration = timedelta(seconds=(end_time - start_time))
    logger.opt(depth=2).debug("{} done. Elapsed time: {}", msg, duration)


def print_mem(msg=None):
    (c, p) = tracemalloc.get_traced_memory()
    logger.debug(f"Mem usage: {c / 1024 / 1024} mb, {p / 1024 / 1024} mb --- {msg}")
