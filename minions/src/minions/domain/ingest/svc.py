from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING

import sh
from loguru import logger
from tifffile import TiffFile

from minions.domain.common.error import DomainError, DomainErrorCode
from minions.domain.ingest.model import OMETiffSrc
from minions.infra.tiledb.readers.ome_tiff import OMETiffReader
from minions.infra.tiledb.writers.tdb import TdbWriter

if TYPE_CHECKING:
    from minions.config.model import MetaConfig
    from minions.domain.disk.svc import DiskSvc
    from minions.domain.tdb.svc import TdbSvc
    from minions.infra.tiledb.readers.base import ImageReader

cache_file_suffix = "-cache.ome.btf"


class IngestSvc:
    def __init__(
        self,
        meta_cfg: MetaConfig,
        tdb_svc: TdbSvc,
        disk_svc: DiskSvc,
    ) -> None:
        self.meta_cfg = meta_cfg
        self.disk_svc = disk_svc

        bf_bin_path = self.meta_cfg.ingest.bf_bin_path
        self.bfconvert = sh.Command("bfconvert", [bf_bin_path])
        self.showinf = sh.Command("showinf", [bf_bin_path])

        self.tdb_svc = tdb_svc

    def generate_omexml(self, source: Path) -> str:
        try:
            res = self.showinf(source, "-omexml-only", "-nopix")
            logger.debug("generate omexml from {source} with success", source=source)
            return res
        except sh.ErrorReturnCode as err:
            logger.error(
                "generate omexml from {source} failed as {error}",
                source=source,
                error=err,
            )
            raise DomainError(DomainErrorCode.get_omexml_failed) from err

    def _get_tmp_ome_file_path(self, src_path: Path):
        ingest_cache_dir_r = self.disk_svc.resolve_to_irs_disk_path(
            Path("_ingest-cache_"),
            "r",
        )
        ingest_cache_dir_w = self.disk_svc.resolve_to_irs_disk_path(
            Path("_ingest-cache_"),
            "w",
        )
        ingest_cache_dir_r.mkdir(exist_ok=True, parents=True)
        ingest_cache_dir_w.mkdir(exist_ok=True, parents=True)

        src_path_str = self.disk_svc.relative_to_irs_disk_path(src_path, mode="r").as_posix()
        file_name = f"{src_path_str}.ome.btf"
        cache_name = f"{src_path_str}.cache.ome.btf"

        tgt_path = ingest_cache_dir_w.joinpath(file_name)
        cache_path_r = ingest_cache_dir_r.joinpath(cache_name)
        cache_path_w = ingest_cache_dir_w.joinpath(cache_name)
        return tgt_path, cache_path_r, cache_path_w

    def to_ome_tiff(self, src_path: Path):
        tgt_path, cache_path_r, cache_path_w = self._get_tmp_ome_file_path(src_path)
        if cache_path_r.exists():
            logger.debug("convert to ome-tiff: use cached file: {}", cache_path_r)
            return cache_path_r

        try:
            self.bfconvert(
                src_path,
                tgt_path,
                "-overwrite",
                # https://bio-formats.readthedocs.io/en/stable/users/comlinetools/index.html#version-checker
                "-no-upgrade",
                "-bigtiff",
                "-noflat",
                "-series",
                "0",
                _out=logger.debug,
            )

            logger.debug(
                "convert to ome-tiff with success:  {source} to {target}",
                source=src_path,
                target=tgt_path,
            )
            tgt_path.replace(cache_path_w)
            return cache_path_r
        except sh.ErrorReturnCode as err:
            logger.error(
                "convert to ome-tiff failed: {source} to {target} => {error}",
                source=src_path,
                target=tgt_path,
                error=err,
            )
            raise DomainError(DomainErrorCode.convert_to_ome_failed) from err

    def can_read_by_tiff(self, source: Path):
        try:
            with TiffFile(source) as tiff:
                is_vsi = tiff.pages.first.is_sis and not tiff.is_sis
                return len(tiff.series) > 0 and not is_vsi
        except Exception:
            return False

    def to_tiff_with_omemeta(self, src_path: Path) -> OMETiffSrc:
        if not src_path.is_absolute():
            src_path = self.disk_svc.resolve_to_irs_disk_path(src_path, "r")

        ome_tiff_src = self.as_ometiff(src_path)
        if ome_tiff_src is None:
            ome_tiff_path = self.to_ome_tiff(src_path)
            with TiffFile(ome_tiff_path) as ome_tiff:
                omemeta = ome_tiff.ome_metadata
                ome_tiff_src = OMETiffSrc(src_path=ome_tiff_path, omemeta=omemeta)

        return ome_tiff_src

    def as_ometiff(self, src_path: Path):
        if self.can_read_by_tiff(src_path):
            omemeta = self.generate_omexml(src_path)
            ome_tiff = OMETiffSrc(src_path=src_path, omemeta=omemeta)
            return ome_tiff
        else:
            return None

    def ome_tiff_to_tdb(self, ome_tiff_src: OMETiffSrc, tdb_group_uri: str):
        with OMETiffReader(ome_tiff_src.src_path, omemeta=ome_tiff_src.omemeta) as ome_tiff_reader:
            self.to_tdb(ome_tiff_reader, tdb_group_uri=tdb_group_uri)

    def to_tdb(
        self,
        reader: ImageReader,
        tdb_group_uri: str,
    ):
        try:
            tdb_writer = TdbWriter(self.tdb_svc, tdb_group_uri)
            tdb_writer.write_from(reader)
        except DomainError:
            raise
        except Exception as err:
            logger.error(
                "ingest into {target} failed as {error}",
                target=tdb_group_uri,
                error=err,
            )

            raise DomainError(DomainErrorCode.convert_to_tiledb_failed) from err
