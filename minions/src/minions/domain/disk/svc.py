from pathlib import Path
from typing import Literal

from minions.config.model import MetaConfig


class DiskSvc:
    def __init__(self, meta_cfg: MetaConfig):
        self.meta_cfg = meta_cfg

    def resolve_to_irs_disk_path(self, disk_path: Path, mode: Literal["r", "w"]) -> Path:
        irs_disk_root = self.get_irs_disk_root_path(mode)
        disk_path = disk_path.relative_to(disk_path.root)
        irs_disk_path = irs_disk_root.joinpath(disk_path).resolve()
        return irs_disk_path

    def get_irs_disk_root_path(self, mode: Literal["r", "w"]) -> Path:
        root_path_prefix = self.meta_cfg.data_root_path.as_posix()
        root_path_with_mode = Path(f"{root_path_prefix}-{mode}").resolve()
        return root_path_with_mode

    def relative_to_irs_disk_path(self, disk_path: Path, mode: Literal["r", "w"]) -> Path:
        irs_disk_root = self.get_irs_disk_root_path(mode)
        relative_path = disk_path.relative_to(irs_disk_root)
        return relative_path
