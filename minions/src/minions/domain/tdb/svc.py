import tiledb

from minions.config.model import MetaConfig
from minions.infra.tiledb.readers.tdb import TdbReader


class TdbSvc:
    def __init__(self, meta_cfg: MetaConfig):
        self.meta_cfg = meta_cfg
        self.tdb_config = get_tdb_config(meta_cfg)

    def get_tdb(self, tdb_group_uri: str):
        tdb = TdbReader(tdb_group_uri, config=self.tdb_config)
        return tdb


def get_tdb_config(meta_cfg: MetaConfig):
    s3 = meta_cfg.s3
    tdb_config = tiledb.Config(
        {
            "vfs.s3.region": s3.region,
            "vfs.s3.endpoint_override": s3.endpoint,
            "vfs.s3.aws_access_key_id": s3.key_id,
            "vfs.s3.aws_secret_access_key": s3.key_secret,
        },
    )
    return tdb_config
