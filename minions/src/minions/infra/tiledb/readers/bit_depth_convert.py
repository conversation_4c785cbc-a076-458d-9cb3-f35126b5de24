import numpy as np
import skimage.exposure as ski

from minions.infra.tiledb.readers.base import ImageReader


class BitDepthConvertReader(ImageReader):
    def __init__(self, src_tdb: ImageReader, tgt_bit_depth: str):
        self.src_tdb = src_tdb
        self.tgt_bit_depth = tgt_bit_depth

    def __enter__(self) -> ImageReader:
        self.src_tdb.__enter__()
        return self

    def __exit__(self, *args, **kwargs):
        if self.src_tdb:
            self.src_tdb.__exit__(*args, **kwargs)

    def axes(self):
        return self.src_tdb.axes()

    def level_count(self):
        return 1

    def level_dtype(self, level):
        return np.dtype(self.tgt_bit_depth)

    def level_shape(self, level):
        return self.src_tdb.level_shape(level)

    def level_image(self, level, slices=None):
        src_data = self.src_tdb.level_image(level, slices)
        tgt_dtype = self.level_dtype(level)
        if tgt_dtype.kind == "f":
            return src_data.astype(tgt_dtype)

        src_dtype = src_data.dtype
        if src_dtype == tgt_dtype:
            return src_data

        # todo: float type image need to cal min/max first or just use 0~1, inrange might need to by channels
        in_range = "image" if src_dtype.kind == "f" else "dtype"
        rescaled_da = ski.rescale_intensity(src_data, in_range=in_range, out_range=self.tgt_bit_depth)
        return rescaled_da

    def image_metadata(self):
        src_meta_copied = self.src_tdb.image_metadata().model_copy(deep=True)
        for ch in src_meta_copied.Channels:
            ch.reset_view_info()
        return src_meta_copied

    def original_metadata(self):
        return None
