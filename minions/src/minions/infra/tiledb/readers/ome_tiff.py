from __future__ import annotations

from typing import TYPE_CHECKING, Any

import numpy as np
import tifffile
import zarr
from tiledb.libtiledb import WebpInputFormat

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import (
    AxesData,
    ImageMeta,
    ImageReader,
    OriginalMeta,
    OriginalMetaType,
)

if TYPE_CHECKING:
    import os
    from collections.abc import Mapping, Sequence


class OMETiffReader(ImageReader):
    def __init__(
        self,
        input_path: os.PathLike,
        omemeta: str | None,
        extra_tags: Sequence[str | int] = (),
    ):
        """OME-TIFF image reader.

        :param input_path: The path to the TIFF image
        :param extra_tags: Extra tags to read, specified either by name or by int code.
        """
        self.input_path = input_path
        self._extra_tags = extra_tags
        self._tiff = tifffile.TiffFile(input_path)
        # TODO(J): try find and read the one with largest resolution
        self._series = self._tiff.series[0]

        self.omemeta = omemeta
        self._metadata = tifffile.xml2dict(omemeta) if omemeta else {}

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self._tiff.close()

    def axes(self) -> Axes:
        # see tifffile.TIFF.AXES_NAMES
        # - **S : sample** (color space and extra samples)
        # - **C : channel** (acquisition path or emission wavelength)

        axes = Axes(self._series.axes.replace("S", Axes.C))
        return axes

    def level_count(self) -> int:
        level_count = len(self._series.levels)
        return level_count

    def level_dtype(self, level: int) -> np.dtype:
        dtype = self._series.levels[level].dtype
        return dtype

    def level_shape(self, level: int):
        l_shape = self._series.levels[level].shape
        shape_with_axes = AxesData[int].from_tuple(axes=self.axes(), data=l_shape)
        return shape_with_axes

    def level_image(self, level: int, slices: tuple[slice, ...] | None = None) -> np.ndarray:
        if slices is None:
            return self._series.levels[level].asarray()
        if not hasattr(self, "_zarr_group"):
            store = self._series.aszarr(multiscales=True)
            self._zarr_group = zarr.open_group(store, mode="r")
        return np.asarray(self._zarr_group[str(level)][slices])  # type: ignore

    def _get_from_meta_annotation(self, key: str, default: Any = None) -> Any:
        try:
            xmlanns = self._metadata["OME"]["StructuredAnnotations"]["XMLAnnotation"]
            for xmlann in xmlanns:
                entry = xmlann["Value"]["OriginalMetadata"]
                if entry["Key"] == key:
                    return entry["Value"]
        except KeyError:
            pass
        return default

    def fill_with(self, tgt: dict, src: Mapping, keys: list[str]):
        for key in keys:
            tgt[key] = src.get(key)

    def image_metadata(self) -> ImageMeta:
        image_meta_dict = {}
        channels = []

        if self._metadata:
            image_meta = self._metadata["OME"]["Image"]
            image_meta: dict = image_meta[0] if isinstance(image_meta, list) else image_meta
            self.fill_with(image_meta_dict, image_meta, ["AcquisitionDate"])
            pixels_meta = image_meta["Pixels"]

            self.fill_with(
                image_meta_dict,
                pixels_meta,
                [
                    "TimeIncrement",
                    "TimeIncrementUnit",
                    "PhysicalSizeX",
                    "PhysicalSizeXUnit",
                    "PhysicalSizeY",
                    "PhysicalSizeYUnit",
                    "PhysicalSizeZ",
                    "PhysicalSizeZUnit",
                ],
            )

            channel_meta = pixels_meta["Channel"]
            if isinstance(channel_meta, list):
                for idx, channel in enumerate(channel_meta):
                    channel_dict = {
                        "id": idx,
                        "name": str(channel.get("Name", f"Channel {idx}")),
                    }

                    self.fill_with(
                        channel_dict,
                        channel,
                        ["EmissionWavelength", "EmissionWavelengthUnit"],
                    )

                    channels.append(channel_dict)
            else:
                for idx in range(channel_meta["SamplesPerPixel"]):
                    channel_metadata = {
                        "id": idx,
                        "name": f"Channel {idx}",
                    }

                    channels.append(channel_metadata)
        else:
            for idx in range(self._series.sizes.get("channel", 0)):
                channel_metadata = {
                    "id": idx,
                    "name": f"Channel {idx}",
                }
                channels.append(channel_metadata)

        image_meta_dict["Channels"] = channels

        meta = ImageMeta.model_validate(image_meta_dict)
        return meta

    def original_metadata(self) -> OriginalMeta | None:
        if self.omemeta is None:
            return None

        meta = OriginalMeta(type=OriginalMetaType.OMEXML, data=self.omemeta)
        return meta

    def webp_format(self) -> WebpInputFormat:
        if self._series.keyframe.photometric == tifffile.PHOTOMETRIC.RGB:
            return WebpInputFormat.WEBP_RGB
        # it is possible that instead of a single RGB channel (samplesperpixel==3)
        # there are 3 MINISBLACK channels (samplesperpixel=1). In this case look for the
        # photometric interpretation in the original metadata
        if self._get_from_meta_annotation("PhotometricInterpretation") == "RGB":
            return WebpInputFormat.WEBP_RGB
        return WebpInputFormat.WEBP_NONE

    def __repr__(self) -> str:
        return f"{type(self).__name__}({self.input_path})"
