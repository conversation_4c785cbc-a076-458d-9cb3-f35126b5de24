from minions.infra.tiledb.filters.base import ImageFilter
from minions.infra.tiledb.readers.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ComposeReader, ImageReader


class FilterReader(ComposeReader):
    def __init__(self, src_reader: ImageReader, img_filter: ImageFilter):
        super().__init__(src_reader)
        self.img_filter = img_filter

    def level_image(self, level: int, slices=None):
        src_arr = self.src_reader.level_image(level, slices)
        axes = self.axes()
        src_data = ArrayData(axes=axes, data=src_arr)
        transformed_arr = self.img_filter.transform(src_data)
        return transformed_arr
