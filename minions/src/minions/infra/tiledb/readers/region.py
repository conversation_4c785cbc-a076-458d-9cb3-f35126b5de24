import math

import numpy as np
from pydantic import BaseModel

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import AxesData, ChannelMeta, ImageReader
from minions.infra.tiledb.readers.tdb import get_default_color_by_id


class Segment(BaseModel):
    start: int
    length: int

    @property
    def end(self):
        return self.start + self.length

    def get_segment_slice(self, relative_slice: slice):
        if relative_slice.start is None:
            start = self.start
        else:
            start = relative_slice.start + self.start

        if relative_slice.stop is None:
            stop = self.end
        else:
            stop = relative_slice.stop + self.start

        return slice(start, stop, relative_slice.step)

    def divide(self, times: float):
        return Segment(start=int(self.start // times), length=int(self.length // times))


class Region(BaseModel):
    x_segment: Segment | None = None
    y_segment: Segment | None = None
    channel_ids: list[int] | None = None

    def get_region_shape(self, level: int, shape: AxesData[int]):
        times_by_level = math.pow(2, level)
        copied_shape = shape.model_copy(deep=True)
        if self.x_segment:
            copied_shape.raw[Axes.X] = int(self.x_segment.length // times_by_level)
        if self.y_segment:
            copied_shape.raw[Axes.Y] = int(self.y_segment.length // times_by_level)
        if self.channel_ids and Axes.C in copied_shape.raw:
            copied_shape.raw[Axes.C] = len(self.channel_ids)

        return copied_shape

    def iter_region_slices(self, level: int, tgt_slices: AxesData[slice]):
        times_by_level = math.pow(2, level)
        copied_slices = tgt_slices.raw.copy()
        if self.x_segment:
            x_slice = tgt_slices.raw.get(Axes.X)
            if x_slice:
                x_segment_by_level = self.x_segment.divide(times_by_level)
                copied_slices[Axes.X] = x_segment_by_level.get_segment_slice(x_slice)
        if self.y_segment:
            y_slice = tgt_slices.raw.get(Axes.Y)
            if y_slice:
                y_segement_by_level = self.y_segment.divide(times_by_level)
                copied_slices[Axes.Y] = y_segement_by_level.get_segment_slice(y_slice)

        c_slice = tgt_slices.raw.get(Axes.C)
        if not self.channel_ids or not c_slice:
            yield AxesData[slice].from_dict(copied_slices).data
        else:
            sorted_channel_ids = sorted(self.channel_ids)
            range_start = c_slice.start or 0
            range_stop = c_slice.stop or len(self.channel_ids)
            range_step = c_slice.step or 1
            for channel_idx_in_slice in range(range_start, range_stop, range_step):
                channel_idx_in_src = sorted_channel_ids[channel_idx_in_slice]
                copied_slices[Axes.C] = slice(
                    channel_idx_in_src,
                    channel_idx_in_src + 1,
                )
                yield AxesData[slice].from_dict(copied_slices).data

    def level_count(self):
        if self.x_segment is None or self.y_segment is None:
            return None
        fit_size = 512
        pyramid_level = math.ceil(
            math.log2(max(self.x_segment.length, self.y_segment.length) / fit_size),
        )

        pyramid_level = max(pyramid_level, 0)

        return pyramid_level + 1


class RegionReader(ImageReader):
    def __init__(self, src_reader: ImageReader, region: Region | None = None):
        self.src_reader = src_reader
        self.region = region or Region()

    def __enter__(self) -> ImageReader:
        self.src_reader.__enter__()
        return self

    def __exit__(self, *args, **kwargs):
        if self.src_reader:
            self.src_reader.__exit__(*args, **kwargs)

    def axes(self):
        return self.src_reader.axes()

    def level_count(self):
        src_level_count = self.src_reader.level_count()
        return self.region.level_count() or src_level_count

    def level_dtype(self, level):
        return self.src_reader.level_dtype(level)

    def level_shape(self, level):
        src_shape = self.src_reader.level_shape(level)
        region_shape = self.region.get_region_shape(level, src_shape)
        return region_shape

    def level_image(self, level, slices=None):
        if slices is None:
            slices = tuple(slice(None) for _ in self.axes().dims)

        slices_with_axes = AxesData[slice].from_tuple(axes=self.axes(), data=slices)

        def _iter_query_all_channels():
            region_slices = self.region.iter_region_slices(level, slices_with_axes)
            for each_ch_region_slices in region_slices:
                each_ch_data = self.src_reader.level_image(level, each_ch_region_slices)
                yield each_ch_data

        c_axis = slices_with_axes.axes.dims.find(Axes.C)
        merged_arr = np.concatenate(list(_iter_query_all_channels()), axis=c_axis)
        return merged_arr

    def image_metadata(self):
        src_meta_copied = self.src_reader.image_metadata().model_copy(deep=True)

        def get_channel_metas():
            channel_ids = self.region.channel_ids or [ch.id for ch in src_meta_copied.Channels]
            for idx, original_ch_idx in enumerate(sorted(channel_ids)):
                name = f"channel-{idx}"
                ch_meta = ChannelMeta(id=idx, name=name)
                for ch in src_meta_copied.Channels:
                    if ch.id == original_ch_idx:
                        ch_meta.name = ch.name
                        # below are for colored_img_write usage
                        ch_meta.min = ch.min
                        ch_meta.max = ch.max
                        ch_meta.view_max = ch.view_max
                        ch_meta.view_min = ch.view_min
                        ch_meta.color = ch.color

                        if ch_meta.color is None:
                            ch_meta.color = get_default_color_by_id(original_ch_idx)

                        break
                yield ch_meta

        src_meta_copied.Channels = list(get_channel_metas())
        return src_meta_copied

    def original_metadata(self):
        return None
