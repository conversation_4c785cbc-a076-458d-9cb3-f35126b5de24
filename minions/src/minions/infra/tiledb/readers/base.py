from abc import ABC, abstractmethod
from collections.abc import Callable, MutableMapping
from contextlib import AbstractContextManager
from datetime import datetime
from enum import StrEnum, auto
from typing import Annotated, Self, TypeVar

import numpy as np
from pydantic import BaseModel, ConfigDict, Field
from tiledb.libtiledb import WebpI<PERSON>putFormat

from minions.infra.tiledb.axes import Axes

TAxesDataType = TypeVar("TAxesDataType")


class AxesData[TAxesDataType](BaseModel):
    axes: Axes
    raw: dict[str, TAxesDataType]

    model_config = ConfigDict(frozen=True, arbitrary_types_allowed=True)

    @classmethod
    def from_tuple(cls, axes: Axes, data: tuple[TAxesDataType, ...]):
        raw = dict(zip(axes.dims, data, strict=True))
        axes_data = cls(axes=axes, raw=raw)
        return axes_data

    @classmethod
    def from_dict(cls, data: dict[str, TAxesDataType]):
        axes = Axes(data.keys()).as_canonical()
        axes_data = cls(axes=axes, raw=data)
        return axes_data

    @property
    def data(self) -> tuple[TAxesDataType, ...]:
        items = (self.raw[dim] for dim in self.axes.dims)
        return tuple(items)

    def canonicalize(
        self,
        filter_func: Callable[[tuple[str, TAxesDataType]], bool] | None = None,
    ):
        """Return a new instance with the dimensions ordered in (TCZYX)."""
        return AxesData[TAxesDataType].from_dict(
            data=dict(filter(filter_func, self.raw.items())),
        )

    def map_to_axes(self, target_axes: Axes) -> tuple[TAxesDataType, ...]:
        values = list(self.data)
        self.axes.mapper(target_axes).transform_sequence(values)
        return tuple(values)


class ArrayData(BaseModel):
    axes: Axes
    data: np.ndarray
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def to_axes_array(self, axes: Axes):
        data_mapped = self.axes.mapper(axes).map_array(self.data)
        return data_mapped


class ArraySelector(BaseModel):
    # image selections
    slices: AxesData[slice] | None = None
    level: int | None = None

    model_config = ConfigDict(arbitrary_types_allowed=True)


class OriginalMetaType(StrEnum):
    OMEXML = auto()


class OriginalMeta(BaseModel):
    # type here define how to parse data below, e.g. it is a ome xml
    # or it is a tdb json str
    type: OriginalMetaType
    data: Annotated[str, Field(repr=False)]


class Histogram(BaseModel):
    hist: list[float]
    bins: list[float]


class ChannelMeta(BaseModel):
    id: int
    name: str
    min: float | None = None
    max: float | None = None
    view_min: float | None = None
    view_max: float | None = None
    view_shown: bool | None = None
    color: str | None = None
    EmissionWavelength: float | None = None
    EmissionWavelengthUnit: str | None = None
    histogram: Histogram | None = None

    def reset_view_info(self):
        self.view_min = None
        self.view_max = None
        self.view_shown = None
        self.histogram = None


class ImageMeta(BaseModel):
    Channels: list[ChannelMeta]
    AcquisitionDate: datetime | None = None

    TimeIncrement: float | None = None
    TimeIncrementUnit: str | None = None

    PhysicalSizeX: float | None = None
    PhysicalSizeXUnit: str | None = None

    PhysicalSizeY: float | None = None
    PhysicalSizeYUnit: str | None = None

    PhysicalSizeZ: float | None = None
    PhysicalSizeZUnit: str | None = None

    view_gamma: float | None = None


class LevelMeta(BaseModel):
    level: int
    axes: str
    shape: tuple[int, ...]
    name: str


class LevelsMeta(BaseModel):
    levels: list[LevelMeta]


class GroupMeta(BaseModel):
    axes: str
    fmt_version: str
    data_version: str
    levels_metadata: LevelsMeta
    image_metadata: ImageMeta
    original_metadata: OriginalMeta | None

    def get_image_metadata_update_kv(self) -> dict:
        return {
            "image_metadata": self.image_metadata.model_dump_json(),
        }

    def to_tdb(self) -> dict[str, str]:
        tdb_dict = {
            "axes": self.axes,
            "fmt_version": self.fmt_version,
            "levels_metadata": self.levels_metadata.model_dump_json(),
            "image_metadata": self.image_metadata.model_dump_json(),
            "data_version": self.data_version,
        }

        if self.original_metadata:
            tdb_dict.update(original_metadata=self.original_metadata.model_dump_json())

        return tdb_dict

    @classmethod
    def from_tdb(cls, tdb: MutableMapping[str, str]) -> Self:
        original_json = tdb.get("original_metadata")
        original_metadata = OriginalMeta.model_validate_json(original_json) if original_json is not None else None

        meta = cls(
            axes=tdb.get("axes", "UNKNOWN"),
            fmt_version=tdb.get("fmt_version", "UNKNOWN"),
            levels_metadata=LevelsMeta.model_validate_json(
                tdb.get("levels_metadata", "{}"),
            ),
            image_metadata=ImageMeta.model_validate_json(
                tdb.get("image_metadata", "{}"),
            ),
            original_metadata=original_metadata,
            data_version=tdb.get("data_version", ""),
        )
        return meta


class ImageReader(AbstractContextManager):
    def __exit__(self, *args, **kwargs):
        return None

    @abstractmethod
    def axes(self) -> Axes:
        """Get the axes of this multi-resolution image."""

    @abstractmethod
    def level_count(self) -> int:
        """Get the number of levels for this multi-resolution image.

        Levels are numbered from 0 (highest resolution) to
        level_count - 1 (lowest resolution).
        """

    @abstractmethod
    def level_dtype(self, level: int) -> np.dtype:
        """Return the dtype of the image for the given level."""

    @abstractmethod
    def level_shape(self, level: int) -> AxesData[int]:
        """Return the shape of the image for the given level."""

    @abstractmethod
    def level_image(
        self,
        level: int,
        slices: tuple[slice, ...] | None = None,
    ) -> np.ndarray:
        """Return the image for the given level as numpy array.

        The axes of the array are specified by the `axes` property.

        :param slices: If not None, a tuple of slices (one per each axes) that specify
        the subregion of the image to return.
        """

    @abstractmethod
    def image_metadata(self) -> ImageMeta:
        """Return the metadata for the whole multi-resolution image."""

    @abstractmethod
    def original_metadata(self) -> OriginalMeta | None:
        """Return the metadata of the original file."""

    def webp_format(self) -> WebpInputFormat:
        """WebpInputFormat of this multi-resolution image. Defaults to WEBP_NONE."""
        return WebpInputFormat.WEBP_NONE


class ComposeReader(ImageReader):
    def __init__(self, src_reader: ImageReader):
        self.src_reader = src_reader

    def __enter__(self) -> ImageReader:
        self.src_reader.__enter__()
        return self

    def __exit__(self, *args, **kwargs):
        if self.src_reader:
            self.src_reader.__exit__(*args, **kwargs)

    def axes(self):
        return self.src_reader.axes()

    def level_count(self):
        return self.src_reader.level_count()

    def level_dtype(self, level):
        return self.src_reader.level_dtype(level)

    def level_shape(self, level):
        return self.src_reader.level_shape(level)

    def level_image(self, level, slices=None):
        src_data = self.src_reader.level_image(level, slices)
        return src_data

    def image_metadata(self):
        # Return a copy of the source metadata
        src_meta_copied = self.src_reader.image_metadata().model_copy(deep=True)
        return src_meta_copied

    def original_metadata(self):
        return None
