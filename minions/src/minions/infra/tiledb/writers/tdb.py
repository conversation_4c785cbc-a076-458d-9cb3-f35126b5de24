from loguru import logger

from minions.domain.tdb.svc import TdbSvc
from minions.infra.tiledb.converters.converter import to_tiledb
from minions.infra.tiledb.readers.base import ImageReader
from minions.infra.tiledb.readers.tdb import ATTR_INTENSITY
from minions.infra.tiledb.writers.base import ImageWriter


class TdbWriter(ImageWriter):
    def __init__(self, tdb_svc: TdbSvc, output_group_uri: str):
        self.output_group_uri = output_group_uri
        self.tdb_svc = tdb_svc

    def write_from(self, src_reader: ImageReader):
        tdb_config = self.tdb_svc.tdb_config

        to_tiledb(
            src_reader,
            output_path=self.output_group_uri,
            attr_name=ATTR_INTENSITY,
            config=tdb_config,
        )

        # use tdb reader to update channel min-max and histogram
        try:
            with self.tdb_svc.get_tdb(self.output_group_uri) as tdb:
                tdb.consolidate(vacuum=True)
                tdb.update_channel_min_max_meta()
        except Exception:
            logger.exception("update channel min/max failed when convert to tiledb")
