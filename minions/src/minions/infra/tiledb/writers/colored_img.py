from enum import StrEnum
from pathlib import Path

import dask.array as da
import numpy as np
import psutil
import skimage as ski
from loguru import logger
from PIL import Image, ImageColor

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.converters.tiles import (
    get_chunk_size,
    iter_slices_by_clamp,
)
from minions.infra.tiledb.readers.base import AxesData, ChannelMeta, ImageReader
from minions.infra.tiledb.readers.tdb import get_default_color_by_id
from minions.infra.tiledb.writers.base import ImageWriter

pil_yx_axes = Axes("YX")


class ColorMode(StrEnum):
    rgb = "RGB"
    rgba = "RGBA"


class ColoredImgWriter(ImageWriter):
    def __init__(
        self,
        output_path: Path,
        write_level: int = 0,
        color_mode: ColorMode = ColorMode.rgb,
        clamp_mem_bytes: int | None = None,
        chunk_cores: int | None = None,
    ):
        self._output_path = output_path
        self.write_level = write_level
        self.color_mode = color_mode
        self.chunk_cores = chunk_cores or psutil.cpu_count() or 1
        self.clamp_mem_bytes = clamp_mem_bytes or self.estimate_clamp_mem()

    def write_from(self, src_reader: ImageReader):
        self._output_path.parent.mkdir(exist_ok=True, parents=True)
        src_axes = src_reader.axes()
        src_to_pil_yx_mapper = src_axes.mapper(pil_yx_axes)
        channels_meta = src_reader.image_metadata().Channels
        level = self.write_level
        level_shape = src_reader.level_shape(level)
        level_dtype = src_reader.level_dtype(level)
        c_shape = level_shape.raw.get(Axes.C)
        xy_size = level_shape.map_to_axes(Axes("XY"))
        pil_image = Image.new(self.color_mode, list(xy_size))

        ch_infos = list(self.get_ch_infos(channels_meta, c_shape or 1))

        for tile_slice in iter_slices_by_clamp(level_shape, level_dtype, self.clamp_mem_bytes):
            logger.debug("start to process tile {}", tile_slice)
            tile = src_reader.level_image(level, tile_slice)
            tile_chunk_size = get_chunk_size(self.chunk_cores, level_shape.axes, tile.shape)
            tile = da.from_array(tile, tile_chunk_size)  # type: ignore

            color_tile_shape = [*list(src_to_pil_yx_mapper.map_shape(tile.shape)), len(self.color_mode)]
            color_merged_data = da.zeros(color_tile_shape, dtype=np.uint16)

            for ch_idx in range(c_shape or 1):
                if c_shape is None:
                    tile_in_one_ch = tile
                else:
                    ch_query_slice = (
                        AxesData[slice].from_dict({Axes.C: slice(ch_idx, ch_idx + 1)}).map_to_axes(src_axes)
                    )
                    tile_in_one_ch = tile[ch_query_slice]

                in_range, color_aligned = ch_infos[ch_idx]

                tile_in_one_ch = ski.exposure.rescale_intensity(tile_in_one_ch, in_range=in_range, out_range=np.float32)  # type: ignore

                tile_in_one_ch = src_to_pil_yx_mapper.map_array(tile_in_one_ch)
                color_in_one_ch = (da.expand_dims(tile_in_one_ch, axis=-1) * color_aligned).astype(np.uint8)

                color_merged_data += color_in_one_ch

            color_merged_data = da.clip(color_merged_data, 0, 255).astype(np.uint8)

            img_tile_slice = src_to_pil_yx_mapper.map_tile(tile_slice)
            pil_image.paste(
                Image.fromarray(color_merged_data.compute(), self.color_mode),
                box=(img_tile_slice[1].start, img_tile_slice[0].start),
            )

        pil_image.save(self._output_path)

    def get_ch_infos(self, channels_meta: list[ChannelMeta], c_shape: int):
        pil_yx_with_color = [1] * len(pil_yx_axes.dims) + [len(self.color_mode)]
        for ch_idx in range(c_shape):
            ch = next((ch for ch in channels_meta if ch.id == ch_idx), None)
            in_range = "dtype"
            color_hex = get_default_color_by_id(ch_idx)

            if ch:
                if ch.min is not None and ch.max is not None:
                    in_range = (ch.view_min or ch.min, ch.view_max or ch.max)
                if ch.color:
                    color_hex = ch.color

            color = ImageColor.getcolor(color_hex, mode=self.color_mode)
            color_aligned = np.asanyarray(color).astype(np.uint8).reshape(pil_yx_with_color)

            yield in_range, color_aligned

    def estimate_clamp_mem(self):
        # TODO, estimate mem peek times and available memory now
        return 1024**3  # 1 GB
