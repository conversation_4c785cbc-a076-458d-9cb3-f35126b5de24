from pathlib import Path

import tifffile

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.converters.tiles import (
    iter_slices_by_clamp,
)
from minions.infra.tiledb.readers.base import AxesD<PERSON>, ImageReader
from minions.infra.tiledb.writers.base import ImageWriter

tiff_strip_shape_axes = Axes("YX")
tiff_img_shape_axes = Axes("CYX")


class OMETiffWriter(ImageWriter):
    def __init__(self, output_path: Path, clamp_mem_bytes: int | None = None):
        self._output_path = output_path
        self.clamp_mem_bytes = clamp_mem_bytes or self.estimate_clamp_mem()

    def write_from(self, src_reader: ImageReader):
        self._output_path.parent.mkdir(exist_ok=True, parents=True)
        src_axes = src_reader.axes()
        img_meta = src_reader.image_metadata()
        channel_infos = [{"Name": channel.name} for channel in img_meta.Channels]
        metadata = {"axes": tiff_img_shape_axes.dims, "Channel": channel_infos}
        image_info = img_meta.model_dump(exclude={"Channels"})
        metadata.update(image_info)

        software = "irs"
        src_to_tiff_axes_mapper = src_axes.mapper(tiff_strip_shape_axes)
        clamp_axes = Axes("Y")  # as we do it by stripe

        with tifffile.TiffWriter(self._output_path, bigtiff=True, ome=True) as writer:
            is_base = True
            level_count = src_reader.level_count()

            for level in range(level_count):
                level_dtype = src_reader.level_dtype(level)
                level_shape_with_axes = src_reader.level_shape(level)
                tiff_img_shape = level_shape_with_axes.map_to_axes(tiff_img_shape_axes)

                tiff_data_iter = (
                    src_to_tiff_axes_mapper.map_array(src_reader.level_image(level, tile_slices))
                    for tile_slices in iter_slices_by_clamp(
                        level_shape_with_axes,
                        level_dtype,
                        self.clamp_mem_bytes,
                        clamp_axes=clamp_axes,
                        fixed_query_shape=AxesData[int].from_dict(
                            {Axes.C: 1}
                        ),  # as we do it by PLANARCONFIG.SEPARATE not by PLANARCONFIG.CONTIG
                    )
                )

                # if need compression, we should write in tile, otherwise we need to do it by pages(a one-time write, as compression only work for pages or tiles)
                # (but tile need to %16 and will do padding if tile data shape not match tile shape, so if with no compression we can avoid padding by using strips )
                # otherwise we need to use tiles with small(512 or 1024) tile size (avoid big padding)

                if is_base:
                    writer.write(
                        tiff_data_iter,
                        shape=tiff_img_shape,
                        dtype=level_dtype,
                        photometric=tifffile.PHOTOMETRIC.MINISBLACK,
                        planarconfig=None,
                        subifds=level_count - 1,
                        metadata=metadata,
                        software=software,
                    )
                    is_base = False
                else:
                    writer.write(
                        tiff_data_iter,
                        shape=tiff_img_shape,
                        dtype=level_dtype,
                        photometric=tifffile.PHOTOMETRIC.MINISBLACK,
                        planarconfig=None,
                        software=software,
                        subfiletype=tifffile.FILETYPE.REDUCEDIMAGE,
                    )

    def estimate_clamp_mem(self):
        # TODO, estimate mem peek times and available memory now
        return 1024**3  # 1 GB
