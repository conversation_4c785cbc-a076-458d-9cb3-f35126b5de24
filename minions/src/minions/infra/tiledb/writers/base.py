from __future__ import annotations

from abc import abstractmethod
from typing import TYPE_CHECKING, Protocol, Self

if TYPE_CHECKING:
    from minions.infra.tiledb.readers.base import ImageReader


class ImageWriter(Protocol):
    def __enter__(self) -> Self:
        return self

    def __exit__(self, *args, **kwargs):
        return

    @abstractmethod
    def write_from(self, src_reader: ImageReader): ...
