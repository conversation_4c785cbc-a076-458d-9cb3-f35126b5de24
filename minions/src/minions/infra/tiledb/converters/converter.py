from __future__ import annotations

import math
from typing import TYPE_CHECKING

import numpy as np
import psutil
import tiledb
from loguru import logger

from minions.domain.common.error import DomainError, DomainErrorCode
from minions.domain.common.utils import timing
from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.converters import FMT_VERSION
from minions.infra.tiledb.converters.group import ReadWriteGroup
from minions.infra.tiledb.converters.scaler import Scaler
from minions.infra.tiledb.converters.tiles import iter_slices_by_clamp
from minions.infra.tiledb.readers.base import (
    AxesData,
)

if TYPE_CHECKING:
    from minions.infra.tiledb.readers.base import ImageReader


# setting a tile to "infinite" effectively makes it equal to the dimension size
_DEFAULT_TILES = AxesData[int].from_dict(
    {Axes.T: 1, Axes.C: 1, Axes.Z: 1, Axes.Y: 512, Axes.X: 512},
)

SUPPORTED_SCALE_AXES = Axes([Axes.X, Axes.Y])
"""
only support XY for now, as opencv resize only do with XY
"""


def to_tiledb(
    reader: ImageReader,
    output_path: str,
    attr_name: str,
    *,
    tiles: AxesData[int] | None = None,
    config: tiledb.Config | None = None,
    base_level: int = 0,
    pyrimid_to_size: int = 1024,
):
    """Convert an image to a TileDB Group of Arrays, one per level.

    :param reader: ImageReader object
    :param output_path: path to the TileDB group of arrays
    :param tiles: A mapping from dimension name (one of 'T', 'C', 'Z', 'Y', 'X') to
        the (maximum) tile for this dimension.
    """
    _check_if_support_ingest(reader)

    max_tiles = _DEFAULT_TILES
    if tiles:
        copied_tiles = _DEFAULT_TILES.raw.copy()
        copied_tiles.update(tiles.raw)
        max_tiles = AxesData[int].from_dict(data=copied_tiles)

    rw_group = ReadWriteGroup(output_path, config=config)

    with rw_group:
        stored_fmt_version = rw_group.r_group.meta.get("fmt_version")
        if stored_fmt_version not in (None, FMT_VERSION):
            logger.warning(
                "incremental ingestion is not supported for different versions: "
                f"current version is {FMT_VERSION}, stored version is {stored_fmt_version} - "
                f"default Fallback: No changes will apply to already ingested image",
            )
            return

        _ingest(
            rw_group=rw_group,
            src_reader=reader,
            max_tiles=max_tiles,
            attr_name=attr_name,
            pyrimid_to_size=pyrimid_to_size,
            base_level=base_level,
        )

    # we need another with here to reopen rw_group,
    # as to reflush and get latest data from r_group read-mode
    with rw_group:
        rw_group.update_group_meta(reader, FMT_VERSION)


def _ingest(
    rw_group: ReadWriteGroup,
    src_reader: ImageReader,
    max_tiles: AxesData[int],
    attr_name: str,
    pyrimid_to_size: int,
    base_level: int,
):
    base_tdb_shape = src_reader.level_shape(base_level).canonicalize()
    base_tdb_dtype = src_reader.level_dtype(base_level)

    pyrimid_level_count = max(math.ceil(math.log2(max(base_tdb_shape.data) / pyrimid_to_size)), 0)
    logger.debug("will ingest {} levels for {} with {}", pyrimid_level_count, base_tdb_shape, base_tdb_dtype)

    previous_level_array_uri = None
    for ingest_level in range(1 + pyrimid_level_count):
        logger.debug("ingest for level {}", ingest_level)
        scale_factor = math.pow(2, ingest_level)

        level_tdb_shape = _get_scaled_axes_shape(
            base_tdb_shape,
            scale_factor,
        )

        level_array_uri = _create_level_array_schema_in_group(
            rw_group,
            level=ingest_level,
            axes_shape=level_tdb_shape,
            max_tiles=max_tiles,
            attr_dtype=base_tdb_dtype,
            compressor=tiledb.ZstdFilter(),
            attr_name=attr_name,
        )

        with rw_group.open_array(level_array_uri, attr_name, "w") as tdb_level_array:
            tdb_level_array.meta.update(level=ingest_level)

            can_from_reader = _reader_with_same_shape(src_reader, ingest_level, level_tdb_shape)

            if previous_level_array_uri is None or can_from_reader:
                _ingest_from_reader(
                    src_reader=src_reader,
                    level=ingest_level,
                    tgt_axes_shape=level_tdb_shape,
                    tgt_tdb=tdb_level_array,
                )

            else:
                _ingest_with_tdb_array_uri(
                    rw_group=rw_group,
                    src_array_uri=previous_level_array_uri,
                    attr_name=attr_name,
                    tgt_tdb=tdb_level_array,
                )

            with timing("array consolidate"):
                tdb_level_array.consolidate()

        previous_level_array_uri = level_array_uri


def _reader_with_same_shape(src_reader: ImageReader, ingest_level: int, tgt_level_shape: AxesData[int]):
    reader_with_same_level_shape = False

    src_reader_levels = src_reader.level_count()
    if src_reader_levels > ingest_level:
        shape_from_src_reader = src_reader.level_shape(ingest_level).map_to_axes(tgt_level_shape.axes)
        reader_with_same_level_shape = np.array_equal(
            shape_from_src_reader,
            tgt_level_shape.data,
        )

    return reader_with_same_level_shape


def _ingest_from_reader(
    src_reader: ImageReader,
    level: int,
    tgt_axes_shape: AxesData[int],
    tgt_tdb: tiledb.DenseArray | tiledb.SparseArray,
):
    src_axes = src_reader.axes()
    tgt_axes = src_axes.as_canonical()
    src_to_tgt_axes_mapper = src_axes.mapper(tgt_axes)
    tgt_to_src_axes_mapper = src_to_tgt_axes_mapper.inverse

    clamped_mem = int(psutil.virtual_memory().available * 0.8)

    for tgt_tile_slices in iter_slices_by_clamp(tgt_axes_shape, tgt_tdb.dtype, clamped_mem):
        logger.debug("ingest tile {}", tgt_tile_slices)
        src_slices = tgt_to_src_axes_mapper.map_tile(tgt_tile_slices)
        src_img_array = src_reader.level_image(level, src_slices)
        logger.debug("data read")
        tgt_tdb[tgt_tile_slices] = src_to_tgt_axes_mapper.map_array(
            src_img_array,
        )
        logger.debug("data write")


def _ingest_with_tdb_array_uri(rw_group: ReadWriteGroup, src_array_uri: str, attr_name: str, tgt_tdb):
    with rw_group.open_array(src_array_uri, attr_name=attr_name, mode="r") as src_tdb:
        scaler = Scaler()
        scaler.apply(src_tdb, tgt_tdb)


def _get_scaled_axes_shape(
    shape_with_axes: AxesData[int],
    scale_factor: float,
):
    scaled_shape_dict = shape_with_axes.raw.copy()

    for dim_name, dim_shape in shape_with_axes.raw.items():
        if dim_name in SUPPORTED_SCALE_AXES.dims:
            scaled_shape = np.rint(dim_shape / scale_factor)
            scaled_shape_dict[dim_name] = scaled_shape

    return AxesData[int].from_dict(scaled_shape_dict)


def _create_level_array_schema_in_group(  # noqa: PLR0913
    rw_group: ReadWriteGroup,
    level: int,
    axes_shape: AxesData[int],
    max_tiles: AxesData[int],
    attr_dtype: np.dtype,
    compressor: tiledb.Filter,
    attr_name: str,
):
    # get or create TileDB array uri
    array_name = f"l_{level}.tdb"

    def schema_factory():
        return _generate_tdb_schema(
            axes_shape,
            max_tiles,
            attr_dtype,
            compressor,
            attr_name,
        )

    if rw_group.get(array_name) is not None:
        logger.warning(
            f"{array_name} exist, try remove first if you need to override schema",
        )
    uri = rw_group.get_or_create_array(array_name, schema_factory)
    return uri


def _generate_tdb_schema(
    axes_shape: AxesData[int],
    max_tiles: AxesData[int],
    attr_dtype: np.dtype,
    compressor: tiledb.Filter,
    attr_name: str,
) -> tiledb.ArraySchema:
    # All dimensions must have the same dtype
    dim_dtype = np.dtype(np.uint32)

    dim_compressor = compressor
    if isinstance(compressor, tiledb.WebpFilter):
        # WEBP Compressor does not accept
        # specific dtypes so for dimensions we use the default
        dim_compressor = tiledb.ZstdFilter()

    dims = []
    # dim order here has impact on performance: https://docs.tiledb.com/main/how-to/arrays/creating-arrays/creating-the-array-domain
    # The order of the dimensions as added to the domain
    # is important later when slicing subarrays.
    # Remember to give priority to more selective dimensions,
    # in order to maximize the pruning power during slicing.
    for dim_name in axes_shape.axes.dims:
        dim_size = axes_shape.raw[dim_name]
        dim_tile = min(dim_size, max_tiles.raw[dim_name])
        dim = tiledb.Dim(
            dim_name,
            (0, dim_size - 1),
            dim_tile,
            dtype=dim_dtype,
            filters=[dim_compressor],
        )
        dims.append(dim)

    attr = tiledb.Attr(name=attr_name, dtype=attr_dtype, filters=[compressor])
    return tiledb.ArraySchema(domain=tiledb.Domain(*dims), attrs=[attr])


def _check_if_support_ingest(reader: ImageReader):
    source_axes_shape = reader.level_shape(level=0)
    for axis in [Axes.T, Axes.Z]:
        axis_dim = source_axes_shape.raw.get(axis)
        if axis_dim and axis_dim > 1:
            raise DomainError(DomainErrorCode.axes_not_support, **{axis: axis_dim})
