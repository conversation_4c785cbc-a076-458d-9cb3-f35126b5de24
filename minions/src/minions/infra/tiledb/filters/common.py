from __future__ import annotations

from typing import TYPE_CHECKING

import dask.array as da
import numpy as np
import skimage.exposure as ski

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.converters.tiles import get_chunk_size
from minions.infra.tiledb.filters.base import ImageFilter

if TYPE_CHECKING:
    from minions.infra.tiledb.readers.base import ArrayData


class ClipFilter(ImageFilter):
    def __init__(self, range_a: list[tuple[float, float]]):
        self.range_a = np.asarray(range_a)

    def transform(self, img_arr: ArrayData):
        shape_to_reshape = [1] * len(img_arr.axes.dims)
        shape_to_reshape[img_arr.axes.dims.index(Axes.C)] = -1
        a_min = np.reshape(self.range_a.T[0], shape_to_reshape)
        a_max = np.reshape(self.range_a.T[1], shape_to_reshape)
        cliped_arr = np.clip(img_arr.data, a_min, a_max)
        return cliped_arr


class RescaleFilter(ImageFilter):
    def __init__(self, range_a: list[tuple[float, float]], cores: int):
        self.range_a = np.asarray(range_a)
        self.cores = cores

    def transform(self, img_arr: ArrayData):
        arr_data = img_arr.data
        arr_axes = img_arr.axes

        shape_to_reshape = [1] * len(arr_axes.dims)
        shape_to_reshape[arr_axes.dims.index(Axes.C)] = -1
        a_min = np.reshape(self.range_a.T[0], shape_to_reshape)
        a_max = np.reshape(self.range_a.T[1], shape_to_reshape)

        chunk_size = get_chunk_size(self.cores, arr_axes, arr_data.shape)
        arr_data_d = da.from_array(arr_data, chunks=chunk_size)
        in_range = (a_min, a_max)
        rescaled_arr = ski.rescale_intensity(
            arr_data_d,
            in_range=in_range,
        ).compute()

        return rescaled_arr
