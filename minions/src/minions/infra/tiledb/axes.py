from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any

import numpy as np
from pyeditdistance.distance import levenshtein

if TYPE_CHECKING:
    from collections.abc import Iterable, Iterator, MutableSequence, Sequence


class AxesMapper(ABC):
    @property
    @abstractmethod
    def inverse(self) -> AxesMapper:
        pass

    @abstractmethod
    def map_array(self, a: np.ndarray) -> np.ndarray:
        pass

    def map_shape(self, shape: tuple[int, ...]) -> tuple[int, ...]:
        """Return the shape of the transformed Numpy array."""
        mapped_shape = list(shape)
        self.transform_shape(mapped_shape)
        return tuple(mapped_shape)

    def map_tile(self, tile: tuple[slice, ...]) -> tuple[slice, ...]:
        """Return the tile for slicing the transformed Numpy array."""
        mapped_tile = list(tile)
        self.transform_tile(mapped_tile)
        return tuple(mapped_tile)

    def transform_shape(self, shape: MutableSequence[int]) -> None:
        """Transform the given shape in place."""
        self.transform_sequence(shape)

    def transform_tile(self, tile: MutableSequence[slice]) -> None:
        """Transform the given tile in place."""
        self.transform_sequence(tile)

    def transform_sequence(self, s: MutableSequence[Any]) -> None:
        """Transform the given mutable sequence in place."""
        # intentionally not decorated as @abstractmethod: subclasses may override
        # transform_shape and transform_tile instead
        raise NotImplementedError


@dataclass(frozen=True)
class Swap(AxesMapper):
    i: int
    j: int

    @property
    def inverse(self) -> AxesMapper:
        return self

    def map_array(self, a: np.ndarray) -> np.ndarray:
        return np.swapaxes(a, self.i, self.j)

    def transform_sequence(self, s: MutableSequence[Any]) -> None:
        i, j = self.i, self.j
        s[i], s[j] = s[j], s[i]


@dataclass(frozen=True)
class Move(AxesMapper):
    i: int
    j: int

    @property
    def inverse(self) -> AxesMapper:
        return Move(self.j, self.i)

    def map_array(self, a: np.ndarray) -> np.ndarray:
        return np.moveaxis(a, self.i, self.j)

    def transform_sequence(self, s: MutableSequence[Any]) -> None:
        s.insert(self.j, s.pop(self.i))


@dataclass(frozen=True)
class Squeeze(AxesMapper):
    idxs: tuple[int, ...]

    @property
    def inverse(self) -> AxesMapper:
        return Unsqueeze(self.idxs)

    def map_array(self, a: np.ndarray) -> np.ndarray:
        return np.squeeze(a, self.idxs)

    def transform_sequence(self, s: MutableSequence[Any]) -> None:
        for i in sorted(self.idxs, reverse=True):
            del s[i]


@dataclass(frozen=True)
class Unsqueeze(AxesMapper):
    idxs: tuple[int, ...]

    @property
    def inverse(self) -> AxesMapper:
        return Squeeze(self.idxs)

    def map_array(self, a: np.ndarray) -> np.ndarray:
        return np.expand_dims(a, self.idxs)

    def transform_sequence(self, s: MutableSequence[Any]):
        first_item = next(iter(s), None)
        if isinstance(first_item, int):
            fill_value = 1
        elif isinstance(first_item, slice):
            fill_value = slice(None)
        else:
            # NOTE: use slice as a workaround for now
            fill_value = slice(None)

        for i in sorted(self.idxs):
            s.insert(i, fill_value)


@dataclass(frozen=True)
class YXC_TO_YX(AxesMapper):  # noqa: N801
    c_size: int

    @property
    def inverse(self) -> AxesMapper:
        return YX_TO_YXC(self.c_size)

    def map_array(self, a: np.ndarray) -> np.ndarray:
        return a.reshape(self.map_shape(a.shape))

    def transform_shape(self, shape: MutableSequence[int]) -> None:
        y, x, c = shape
        if c != self.c_size:
            msg = f"C dimension must have size {self.c_size}: {c} given"
            raise ValueError(msg)
        shape[1] *= c
        del shape[2]

    def transform_tile(self, tile: MutableSequence[slice]) -> None:
        y, x, c = tile
        c_slice = slice(0, self.c_size)
        if c != c_slice:
            msg = f"C dimension can cannot be sliced: {c} given - {c_slice} expected"
            raise ValueError(msg)
        # change x dimension
        tile[1] = slice(x.start * self.c_size, x.stop * self.c_size)
        # remove c dimension
        del tile[2]


@dataclass(frozen=True)
class YX_TO_YXC(AxesMapper):  # noqa: N801
    c_size: int

    @property
    def inverse(self) -> AxesMapper:
        return YXC_TO_YX(self.c_size)

    def map_array(self, a: np.ndarray) -> np.ndarray:
        return a.reshape(self.map_shape(a.shape))

    def transform_shape(self, shape: MutableSequence[int]) -> None:
        c = self.c_size
        shape[1] //= c
        shape.append(c)

    def transform_tile(self, tile: MutableSequence[slice]) -> None:
        c = self.c_size
        tile[1] = slice(tile[1].start // c, tile[1].stop // c)
        tile.append(slice(0, c))


@dataclass(frozen=True)
class CompositeAxesMapper(AxesMapper):
    mappers: Sequence[AxesMapper]

    @property
    def inverse(self) -> AxesMapper:
        return CompositeAxesMapper([t.inverse for t in reversed(self.mappers)])

    def map_array(self, a: np.ndarray) -> np.ndarray:
        for mapper in self.mappers:
            a = mapper.map_array(a)
        return a

    def transform_shape(self, shape: MutableSequence[int]) -> None:
        for mapper in self.mappers:
            mapper.transform_shape(shape)

    def transform_tile(self, tile: MutableSequence[slice]) -> None:
        for mapper in self.mappers:
            mapper.transform_tile(tile)

    def transform_sequence(self, s: MutableSequence[Any]) -> None:
        for mapper in self.mappers:
            mapper.transform_sequence(s)


@dataclass()
class Axes:
    X = "X"
    Y = "Y"
    Z = "Z"
    T = "T"
    C = "C"
    dims: str
    CANONICAL_DIMS = "TCZYX"

    def __init__(self, dims: Iterable[str], *, canonical: bool = False):
        if not isinstance(dims, str):
            dims = "".join(dims)
        dims_set = set(dims)
        if len(dims_set) == 0:
            msg = "Need at least one axis"
            raise ValueError(msg)
        if len(dims) != len(dims_set):
            msg = f"Duplicate axes: {dims}"
            raise ValueError(msg)

        dims_set.difference_update(self.CANONICAL_DIMS)
        if dims_set:
            msg = f"{dims_set} is not a valid Axis in {self.CANONICAL_DIMS}"
            raise ValueError(msg)

        if canonical:
            dims = "".join(dim for dim in self.CANONICAL_DIMS if dim in dims)
        self.dims = dims

    def mapper(self, other: Axes) -> AxesMapper:
        """Return an AxesMapper from this axes to other."""
        return CompositeAxesMapper(list(_iter_axes_mappers(self.dims, other.dims)))

    def as_canonical(self):
        return Axes(self.dims, canonical=True)


def _iter_axes_mappers(s: str, t: str) -> Iterator[AxesMapper]:
    s_set = frozenset(s)
    t_set = frozenset(t)

    common, squeeze_axes = [], []
    for i, m in enumerate(s):
        if m in t_set:
            common.append(m)
        else:
            squeeze_axes.append(i)
    if squeeze_axes:
        # source has extra dims: squeeze them
        yield Squeeze(tuple(squeeze_axes))
        s = "".join(common)
        s_set = frozenset(s)

    missing = t_set - s_set
    if missing:
        # source has missing dims: expand them
        yield Unsqueeze(tuple(range(len(missing))))
        s = "".join(missing) + s
        s_set = frozenset(s)

    # source has the same dims: transpose them
    n = len(s)
    sbuf = bytearray(s.encode())
    tbuf = t.encode()
    while sbuf != tbuf:
        min_distance = np.inf
        best_transpose = None
        for candidate_transpose in _iter_transpositions(n):
            buf = bytearray(sbuf)
            candidate_transpose.transform_sequence(buf)
            distance = levenshtein(buf.decode(), t)
            if distance < min_distance:
                best_transpose = candidate_transpose
                min_distance = distance

        if best_transpose is not None:
            yield best_transpose
            best_transpose.transform_sequence(sbuf)


def _iter_transpositions(n: int) -> Iterator[AxesMapper]:
    for i in range(n):
        for j in range(i + 1, n):
            yield Swap(i, j)
            yield Move(i, j)
            yield Move(j, i)
