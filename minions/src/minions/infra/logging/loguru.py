import logging
import os
import sys

from loguru import logger

from minions.config.model import APPENV, AppConfig, LoggingConfig


class LoguruHandler(logging.Handler):
    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 0
        while (
            frame.f_code.co_filename != record.pathname
            or frame.f_code.co_name != record.funcName
        ):
            frame = frame.f_back
            depth += 1

            if frame is None:
                break

        logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            record.getMessage(),
        )


def adapt_standard_logging(config: LoggingConfig):
    loguru_handler = LoguruHandler()
    for logger_info in config.loggers:
        logger_from_logging = logging.getLogger(logger_info.name)
        logger_from_logging.setLevel(logger_info.level)
        logger_from_logging.handlers = [loguru_handler]
        logger_from_logging.propagate = False


def setup_logging(app_config: AppConfig):
    logger.remove()

    app_env = app_config.app_env
    diagnose_in_non_prod = app_env != APPENV.prod  # avoid leaking sensitive data

    logging_config = app_config.logging

    default_level = os.getenv("LOGURU_LEVEL", "DEBUG")

    logger_filter_dict = {
        logger.name: logger.level for logger in logging_config.loggers
    }

    logger.configure(
        handlers=[
            {
                "sink": sys.stdout,
                "filter": logger_filter_dict,
                "format": logging_config.format,
                "level": default_level,
                "diagnose": diagnose_in_non_prod,
            },  # type: ignore
        ],
    )

    # adapte below loggers to loguru style
    adapt_standard_logging(logging_config)
    return logger
