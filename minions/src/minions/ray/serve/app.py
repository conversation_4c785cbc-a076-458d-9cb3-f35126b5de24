import ray.serve
from pydantic import BaseModel

from minions.ray.serve.api import RayAPI


class AppArgs(BaseModel):
    local_dev: bool | None = None


def create_ray_app(args: AppArgs) -> ray.serve.Application:
    if args.local_dev:
        # only needed in local mode, as kuberay on prod will do it for us
        ray.init(
            resources={
                "get_min_max_info": 2,
                "cpu_bound_default": 1,
                "bf_convert": 2,
                "ray_api_serve": 1,
            },
        )

    app = RayAPI.options(
        name="ray_api",
        ray_actor_options={
            "resources": {"ray_api_serve": 1},
            "num_cpus": 0,
        },
    ).bind()
    return app
