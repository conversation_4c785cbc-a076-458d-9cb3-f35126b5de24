import asyncio
from pathlib import Path

import ray
import ray.serve
import ray.util.state
from fastapi import FastAPI

from minions.di import DI, setup_di
from minions.infra.logging.loguru import setup_logging
from minions.ray.serve.services import (
    IngestReq,
    RayServeReq,
    RayTask,
    get_func_from_ray_task,
    get_resources_from_ray_task,
)


async def do_ray_task(ray_task: RayTask, di: DI, **kwargs):
    func = get_func_from_ray_task(ray_task, di)
    if func is None:
        msg = f"task: {ray_task} not support"
        raise ValueError(msg)

    res = func(**kwargs)
    if asyncio.iscoroutine(res):
        return await res
    else:
        return res


@ray.remote
class RayReqHandler:
    def __init__(self):
        di = setup_di()
        app_config = di.app_config()
        self.logger = setup_logging(app_config)
        self.di = di

    async def do_task(self, req: RayServeReq):
        self.logger.info("do task req => {}", req)

        if req.task_type == RayTask.ingest:
            res = await self.handle_ingest(req.task_args)
            return res
        else:
            res = await self.run_ray_task_by_args(req.task_type, **req.task_args)
            return res

    async def run_ray_task_by_args(self, ray_task: RayTask, **kwargs):
        res = await do_ray_task(ray_task=ray_task, di=self.di, **kwargs)
        return res

    async def handle_ingest(self, task_args: dict):
        req = IngestReq.model_validate(task_args)

        src_path = Path(req.source_path)
        tdb_group_uri = req.output_path

        ingest_svc = self.di.ingest_svc()
        ome_tiff_src = ingest_svc.to_tiff_with_omemeta(src_path)
        ome_tiff_src_ref = ray.put(ome_tiff_src)
        resources = get_resources_from_ray_task(RayTask.ome_tiff_to_tdb)
        actor = RayReqHandler.options(num_cpus=0, resources=resources).remote()
        to_tdb_res = await actor.run_ray_task_by_args.remote(
            RayTask.ome_tiff_to_tdb,
            ome_tiff_src=ome_tiff_src_ref,
            tdb_group_uri=tdb_group_uri,
        )
        return to_tdb_res


api = FastAPI()


@ray.serve.deployment()
@ray.serve.ingress(api)
class RayAPI:
    def __init__(self, *args):  # noqa: ARG002
        di = setup_di()
        app_config = di.app_config()
        self.logger = setup_logging(app_config)
        self.di = di

    @api.post("/start-task/{task_id}")
    def start_task(self, task_id: str, req: RayServeReq):
        self.logger.debug("start-task => {} {}", task_id, req)
        resources = get_resources_from_ray_task(req.task_type)
        task_actor = RayReqHandler.options(
            name=task_id,
            get_if_exists=True,
            num_cpus=0,
            resources=resources,
        ).remote()

        res = task_actor.do_task.remote(req)
        return res.task_id().hex()

    @api.post("/call-task")
    async def call_task(self, req: RayServeReq):
        self.logger.debug("call-task => {}", req)
        resources = get_resources_from_ray_task(req.task_type)
        task_actor = RayReqHandler.options(num_cpus=0, resources=resources).remote()

        try:
            res = await task_actor.do_task.remote(req)
            return res
        except asyncio.CancelledError:
            self.logger.debug("user canncelld, so kill the actor")
            ray.kill(task_actor)

    @api.post("/kill-task/{actor_name}")
    async def kill_task(self, actor_name: str):
        try:
            actor = ray.get_actor(actor_name)
            ray.kill(actor)
        except ValueError as err:
            self.logger.warning("actor {} not found to kill: {}", actor_name, err)

    @api.get("/task-state/{task_id}")
    async def task_state(self, task_id: str):
        state = ray.util.state.get_task(task_id)
        return state
