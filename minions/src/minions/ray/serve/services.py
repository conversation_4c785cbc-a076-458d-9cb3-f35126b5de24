from __future__ import annotations

from enum import Str<PERSON>num, auto
from typing import TYPE_CHECKING

from pydantic import BaseModel

if TYPE_CHECKING:
    from minions.di import DI


class RayTask(StrEnum):
    ome_tiff_to_tdb = auto()
    ingest = auto()
    save_as = auto()
    register_and_merge = auto()
    merge_channels = auto()


def get_func_from_ray_task(ray_task: RayTask, di: DI):
    task_map = {
        RayTask.ome_tiff_to_tdb: di.ingest_svc().ome_tiff_to_tdb,
        RayTask.save_as: di.img_filter_svc().save_as,
        # RayTask.register_and_merge: di.register_svc().register_and_merge,
        # RayTask.merge_channels: di.channel_merge_svc().merge_channels,
    }
    task_func = task_map.get(ray_task)
    return task_func


class ResourceType(StrEnum):
    cpu_bound_default = auto()
    get_min_max_info = auto()
    bf_convert = auto()


ray_task_resource_cfg = {
    # as ingest will do bf_convert first, so let it use the same resource
    RayTask.ingest: {ResourceType.bf_convert: 1},
}


def get_resources_from_ray_task(ray_task: RayTask):
    resource = ray_task_resource_cfg.get(ray_task, {ResourceType.cpu_bound_default: 1})
    return resource


class RayServeReq(BaseModel):
    task_type: RayTask
    task_args: dict


class IngestReq(BaseModel):
    # project/proj-id-xxx/disk/y1/y2/y3.vsi
    source_path: str
    # s3://bucket/project/proj-id-xxx/item/item-id/
    output_path: str
