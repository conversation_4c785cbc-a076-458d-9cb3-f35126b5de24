[project]
name = "minions"
version = "0.1.0"
description = "Add your description here"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.12"
dependencies = [
    "dask>=2025.7.0,<2026.0.0",
    "dependency-injector[yaml]>=4.48.1,<5.0.0",
    "fast-histogram>=0.14,<0.15",
    "fastapi[standard]>=0.116.1,<0.117.0",
    "loguru>=0.7.3,<0.8.0",
    "numpy>=2.3.2,<3.0.0",
    "opencv-python-headless>=*********,<*******",
    "psutil>=7.0.0,<8.0.0",
    "pyeditdistance>=1.0.1,<2.0.0",
    "ray[serve]>=2.48.0,<3.0.0",
    "scikit-image>=0.25.2,<0.26.0",
    "sh>=2.2.2,<3.0.0",
    "tifffile[zarr]>=2025.6.11,<2026.0.0",
    "tiledb>=0.34.2,<0.35.0",
]

[build-system]
requires = ["uv_build>=0.7.13,<0.8"]
build-backend = "uv_build"

[tool.uv]
add-bounds = "major"

[[tool.uv.index]]
name = "mirrors"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
default = true

[tool.uv.workspace]
members = ["t/t"]


[tool.ruff]
# Allow lines to be as long as 120.
line-length = 120

[tool.ruff.lint]
# select = [
#     "FAST",
#     "ASYNC",
#     "FBT",
#     "B",
#     "A",
#     "COM",
#     "C4",
#     "DTZ",
#     "EM",
#     "FIX",
#     "FA",
#     "PIE",
#     "T20",
#     "PT",
#     "Q",
#     "RET",
#     "SLF",
#     "SIM",
#     "SLOT",
#     "TID",
#     "TD",
#     "TC",
#     "PTH",
#     "FLY",
#     "I"
#     "C90"
#     # might
#     "S",
#     "INT",
#     "ISC",
#     "ICN",
#     "LOG",
#     "G",
#     "ARG",

#     "E4",
#     "E7",
#     "E9",
#     "F",
#     "UP",
#     "N",
# ]
select = ["ALL"]
ignore = [
    "D1",
    "INP001",
    "ANN",
    "BLE001",
    "RET504",
    "PGH003",
    "RET505",
    "PLE1205",
    "TRY300",
    "TD003",
]

[dependency-groups]
dev = [
    "ipython>=9.5.0,<10.0.0",
    "pytest>=8.4.1,<9.0.0",
]
