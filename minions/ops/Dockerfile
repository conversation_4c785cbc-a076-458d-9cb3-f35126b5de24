FROM python:3.12-slim

# this is for ray-cluster pod (wget,set path) and jre for bftools
RUN apt-get update && apt-get install -y --no-install-recommends wget curl unzip openjdk-21-jre-headless \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean


COPY --from=ghcr.io/astral-sh/uv:0.8.4 /uv /uvx /bin/

ENV PATH="/app/.venv/bin:$PATH" \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

WORKDIR /app

# download is slow, so we don't embed it into image
RUN curl -L https://github.com/ome/bioformats/releases/download/v8.2.0/bftools.zip -o ./bftools.zip \
    && unzip -n ./bftools.zip -d ./

COPY pyproject.toml uv.lock ./

# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --locked --no-install-project --no-editable 

COPY ./src ./src
# Sync the project
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --locked --no-editable

COPY ./config/ ./config




