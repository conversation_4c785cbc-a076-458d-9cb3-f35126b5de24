import numpy as np

from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.filters.common import ClipFilter
from minions.infra.tiledb.readers.base import AxesData, ChannelMeta, ImageMeta, ImageReader
from minions.infra.tiledb.readers.filter import FilterReader


class NumpyCYXReader(ImageReader):
    def __init__(self, arr: np.ndarray):
        self.arr = arr
        assert arr.ndim == 3

    def axes(self):
        return Axes("CYX")

    def level_count(self):
        return 1

    def level_dtype(self, level):
        return self.arr.dtype

    def level_shape(self, level):
        return AxesData[int].from_tuple(self.axes(), self.arr.shape)

    def level_image(self, level, slices=None):
        if slices is None:
            slices = slice(None)
        src_data = self.arr[slices]
        return src_data

    def image_metadata(self):
        channel_nums = self.arr.shape[0]
        channels = [ChannelMeta(id=i, name=str(i)) for i in range(channel_nums)]
        meta = ImageMeta(Channels=channels)
        return meta

    def original_metadata(self):
        return None


def test_filter_reader_with_clip():
    shape = (4, 4, 4)
    src_reader = NumpyCYXReader(arr=np.arange(np.prod(shape)).reshape(shape))
    range_a = [(8, 10), (24, 26), (40, 42), (58, 60)]
    clip_filter = ClipFilter(range_a=range_a)
    r = FilterReader(src_reader, clip_filter)
    res = r.level_image(0)

    assert np.all(res[0] >= 4)
    assert np.all(res[1] >= 24)
    assert np.all(res[2] >= 40)
    assert np.all(res[3] >= 58)
