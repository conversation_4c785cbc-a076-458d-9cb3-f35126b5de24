import numpy as np
import pytest

from minions.infra.tiledb.readers.flip import flip_slice_with_size


def test_empty_slice():
    """Test slice that selects no elements."""
    # Verify using property-based approach
    size = 10
    original_slice = slice(5, 5)
    result = flip_slice_with_size(original_slice, size)

    # Verify the property holds
    arr = np.arange(size)
    flipped_arr = np.flip(arr)
    np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])


def test_edge_case_negative_start():
    """Test edge case where new_start >= size_in_axis and gets set to None."""
    # This condition is triggered when slice.indices() returns start = -1
    # which happens with very negative start values and negative step
    size = 5
    original_slice = slice(-100, None, -1)  # Very negative start with negative step

    result = flip_slice_with_size(original_slice, size)

    arr = np.arange(size)
    flipped_arr = np.flip(arr)
    np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])


def test_property_based_verification():
    """Test that the flip property holds: np.flip(arr)[s] == arr[flipped_s]."""
    # Test with various array sizes and slice configurations
    test_cases = [
        (10, slice(2, 8)),  # Basic forward slice
        (10, slice(None)),  # Full slice
        (10, slice(0, 10, 2)),  # Positive step
        (10, slice(9, -1, -1)),  # Negative step
        (20, slice(5, 15)),  # Mid-range slice
        (20, slice(None, None, 3)),  # Step with None bounds
        (15, slice(10, 2, -2)),  # Backward slice
        (10, slice(3, 4)),  # Single element
        (1, slice(None)),  # Edge case: size 1
        (10, slice(0, 10, 5)),  # Large step
    ]

    for size, original_slice in test_cases:
        # Create test array
        arr = np.arange(size)

        # Get flipped slice
        flipped_slice = flip_slice_with_size(original_slice, size)

        # Verify the property: np.flip(arr)[original_slice] == arr[flipped_slice]
        flipped_arr = np.flip(arr)

        # Handle empty slices
        original_result = flipped_arr[original_slice]
        flipped_result = arr[flipped_slice]

        np.testing.assert_array_equal(
            original_result,
            flipped_result,
            err_msg=f"Failed for size={size}, slice={original_slice}, flipped_slice={flipped_slice}",
        )


@pytest.mark.parametrize("size", [1, 2, 5, 10, 100])
@pytest.mark.parametrize(
    ("start", "stop", "step"),
    [
        (0, None, 1),
        (None, None, 1),
        (1, 5, 1),
        (0, 10, 2),
        (9, -1, -1),
        (5, 0, -1),
    ],
)
def test_parametrized_slices(size, start, stop, step):
    """Parametrized test for various slice configurations."""
    if stop is not None and stop > size:
        stop = size
    if start is not None and start >= size:
        start = size - 1

    original_slice = slice(start, stop, step)
    flipped_slice = flip_slice_with_size(original_slice, size)

    # Verify with actual arrays
    arr = np.arange(size)
    flipped_arr = np.flip(arr)

    original_result = flipped_arr[original_slice]
    flipped_result = arr[flipped_slice]

    np.testing.assert_array_equal(original_result, flipped_result)
