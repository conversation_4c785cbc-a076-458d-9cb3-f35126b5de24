import numpy as np
import pytest

from minions.infra.tiledb.readers.flip import flip_slice_with_size


def test_basic_forward_slice():
    """Test basic forward slice like slice(2, 5) with size 10."""
    # For array of size 10, slice(2, 5) should become slice(7, 4, -1)
    result = flip_slice_with_size(slice(2, 5), 10)
    expected = slice(7, 4, -1)
    assert result == expected


def test_edge_case_negative_start():
    """Test edge case where new_start >= size_in_axis and gets set to None."""
    # This condition is triggered when slice.indices() returns start = -1
    # which happens with very negative start values and negative step
    size = 5
    original_slice = slice(-100, None, -1)  # Very negative start with negative step

    result = flip_slice_with_size(original_slice, size)

    arr = np.arange(size)
    flipped_arr = np.flip(arr)
    np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])


@pytest.mark.parametrize(
    ("size", "start", "stop", "step"),
    [
        # Basic test cases from property_based_verification
        (10, 2, 8, 1),  # Basic forward slice
        (10, None, None, 1),  # Full slice
        (10, 0, 10, 2),  # Positive step
        (10, 9, -1, -1),  # Negative step
        (20, 5, 15, 1),  # Mid-range slice
        (20, None, None, 3),  # Step with None bounds
        (15, 10, 2, -2),  # Backward slice
        (10, 3, 4, 1),  # Single element
        (1, None, None, 1),  # Edge case: size 1
        (10, 0, 10, 5),  # Large step
        (10, 5, 5, 1),  # Empty slice
        # Additional unique parametrized cases
        (1, 0, None, 1),  # Size 1 variations
        (2, 1, 2, 1),  # Size 2 variations
        (2, 0, 2, 2),
        (2, 1, -1, -1),
        (2, 1, 0, -1),
        (5, 0, None, 1),  # Size 5 variations
        (5, None, None, 1),
        (5, 1, 5, 1),
        (5, 0, 5, 2),
        (5, 4, -1, -1),
        (5, 4, 0, -1),
        (10, 1, 5, 1),  # Size 10 additional cases
        (10, 5, 0, -1),
        (100, 0, None, 1),  # Size 100 variations
        (100, None, None, 1),
        (100, 1, 5, 1),
        (100, 0, 10, 2),
        (100, 9, -1, -1),
        (100, 5, 0, -1),
    ],
)
def test_comprehensive_slice_flipping(size, start, stop, step):
    """Comprehensive parametrized test for various slice configurations."""
    original_slice = slice(start, stop, step)
    flipped_slice = flip_slice_with_size(original_slice, size)

    # Verify the property: np.flip(arr)[original_slice] == arr[flipped_slice]
    arr = np.arange(size)
    flipped_arr = np.flip(arr)

    original_result = flipped_arr[original_slice]
    flipped_result = arr[flipped_slice]

    np.testing.assert_array_equal(
        original_result,
        flipped_result,
        err_msg=f"Failed for size={size}, slice={original_slice}, flipped_slice={flipped_slice}",
    )
