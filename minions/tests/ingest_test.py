from pathlib import Path

from minions.di import DI


def test_ingest_cache_path(di: DI):
    ingest_svc = di.ingest_svc()
    src_path = Path("../../local-data-r/test.vsi")
    tgt_path, cache_path = ingest_svc._get_tmp_ome_file_path(src_path)
    assert tgt_path == Path("../../local-data-w/_ingest-cache_/test.vsi.ome.btf").resolve()
    assert cache_path == Path("../../local-data-r/_ingest-cache_/test.vsi.cache.ome.btf").resolve()


def test_ingest_tdb(di: DI):
    ingest_svc = di.ingest_svc()
    src_path = Path("small/Tonsil-截图.vsi")
    ome_tiff_src = ingest_svc.to_tiff_with_omemeta(src_path)
    ingest_svc.ome_tiff_to_tdb(ome_tiff_src, "../../local-data/tdb-local-test")
